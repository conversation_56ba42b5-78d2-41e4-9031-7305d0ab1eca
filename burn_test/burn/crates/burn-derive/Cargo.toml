[package]
authors = ["nathanielsimard <<EMAIL>>"]
categories = ["science"]
description = "Derive crate for the Burn framework"
edition.workspace = true
keywords = []
license.workspace = true
name = "burn-derive"
readme.workspace = true
repository = "https://github.com/tracel-ai/burn/tree/main/crates/burn-derive"
version.workspace = true

[lints]
workspace = true

[lib]
proc-macro = true

[dependencies]
proc-macro2 = { workspace = true }
quote = { workspace = true }
syn = { workspace = true }
derive-new = { workspace = true }
