[package]
authors = ["nathanielsimard <<EMAIL>>"]
categories = ["science"]
description = "Generic backend that can be compiled just-in-time to any shader language target"
documentation = "https://docs.rs/burn-cubecl"
edition.workspace = true
keywords = ["deep-learning", "machine-learning", "gpu"]
license.workspace = true
name = "burn-cubecl"
readme.workspace = true
repository = "https://github.com/tracel-ai/burn/tree/main/crates/burn-cubecl"
version.workspace = true

[lints]
workspace = true

[features]
autotune = ["burn-cubecl-fusion?/autotune"]
autotune-checks = [
    "autotune",
    "cubecl/autotune-checks",
    "burn-cubecl-fusion?/autotune-checks",
]
memory-checks = ["burn-fusion?/memory-checks"]
default = [
    "autotune",
    "std",
    "fusion",
    "cubecl/default",
    "burn-fusion?/default",
    "burn-cubecl-fusion?/default",
]
doc = ["default"]
export_tests = [
    # Too expensive and flaky for the CI.
    # "memory-checks", 
    # "autotune-checks",
    "burn-tensor-testgen",
    "serial_test",
    "burn-autodiff/export_tests",
    "burn-tensor/export_tests",
    "burn-ndarray",
    "fusion",
    "paste",
]
fusion = ["burn-fusion", "burn-cubecl-fusion"]
fusion-experimental = ["fusion"]
std = ["cubecl/std", "burn-tensor/std", "burn-fusion?/std", "burn-cubecl-fusion?/std"]

template = []

[dependencies]
burn-common = { path = "../burn-common", version = "0.19.0" }
burn-fusion = { path = "../burn-fusion", version = "0.19.0", default-features = false, optional = true }
burn-cubecl-fusion = { path = "../burn-cubecl-fusion", version = "0.19.0", default-features = false, optional = true }
burn-ir = { path = "../burn-ir", version = "0.19.0", default-features = false }
burn-tensor = { path = "../burn-tensor", version = "0.19.0", default-features = false, features = [
    "cubecl",
] }
cubecl = { workspace = true, features = [
    "matmul",
    "convolution",
    "reduce",
    "random",
    "stdlib",
] }

bytemuck = { workspace = true }
derive-new = { workspace = true }
half = { workspace = true, features = ["bytemuck"] }
log = { workspace = true }
num-traits = { workspace = true }
rand = { workspace = true }
spin = { workspace = true }

# Async
futures-lite = { workspace = true, features = ["std"] }

# Template
serde = { workspace = true }
text_placeholder = { workspace = true, features = ["struct_context"] }

burn-tensor-testgen = { path = "../burn-tensor-testgen", version = "0.19.0", optional = true }
hashbrown = { workspace = true }

# When exporting tests
burn-autodiff = { path = "../burn-autodiff", version = "0.19.0", default-features = false, optional = true }
burn-ndarray = { path = "../burn-ndarray", version = "0.19.0", optional = true }
paste = { workspace = true, optional = true }
serial_test = { workspace = true, optional = true }

[package.metadata.docs.rs]
features = ["doc"]
rustdoc-args = ["--cfg", "docsrs"]
