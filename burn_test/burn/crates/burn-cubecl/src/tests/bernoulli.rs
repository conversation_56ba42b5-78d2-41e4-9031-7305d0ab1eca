#[burn_tensor_testgen::testgen(bernoulli)]
mod tests {
    use super::*;

    // Use the reexported test dependency.
    use burn_cubecl::tests::serial_test;
    use serial_test::serial;

    use core::f32;

    use burn_tensor::{Distribution, Shape, Tensor, backend::Backend};

    use cubecl::random::{
        assert_number_of_1_proportional_to_prob, assert_wald_wolfowitz_runs_test,
    };

    #[test]
    #[serial]
    fn number_of_1_proportional_to_prob() {
        TestBackend::seed(0);
        let shape: Shape = [40, 40].into();
        let device = Default::default();
        let prob = 0.7;

        let tensor =
            Tensor::<TestBackend, 2>::random(shape.clone(), Distribution::<PERSON><PERSON><PERSON>(prob), &device)
                .into_data();

        let numbers = tensor
            .as_slice::<<TestBackend as Backend>::FloatElem>()
            .unwrap();

        assert_number_of_1_proportional_to_prob(numbers, prob as f32);
    }

    #[test]
    #[serial]
    fn wald_wolfowitz_runs_test() {
        TestBackend::seed(0);
        let shape = Shape::new([512, 512]);
        let device = Default::default();
        let tensor = Tensor::<TestBackend, 2>::random(shape, Distribution::Bernoulli(0.5), &device);

        let data = tensor.into_data();
        let numbers = data
            .as_slice::<<TestBackend as Backend>::FloatElem>()
            .unwrap();

        // High bound slightly over 1 so 1.0 is included in second bin
        assert_wald_wolfowitz_runs_test(numbers, 0., 1.1);
    }
}
