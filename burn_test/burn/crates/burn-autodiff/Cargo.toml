[package]
authors = ["nathanielsimard <<EMAIL>>"]
categories = ["science"]
description = "Automatic differentiation backend for the Burn framework"
edition.workspace = true
keywords = ["deep-learning", "machine-learning", "data"]
license.workspace = true
name = "burn-autodiff"
readme.workspace = true
repository = "https://github.com/tracel-ai/burn/tree/main/crates/burn-autodiff"
documentation = "https://docs.rs/burn-autodiff"
version.workspace = true

[lints]
workspace = true

[features]
default = ["std"]
export_tests = ["burn-tensor-testgen"]
std = []
async = [] # Require std

[dependencies]
burn-common = { path = "../burn-common", version = "0.19.0", default-features = false }
burn-tensor = { path = "../burn-tensor", version = "0.19.0", default-features = false }
burn-tensor-testgen = { path = "../burn-tensor-testgen", version = "0.19.0", optional = true }

derive-new = { workspace = true }
spin = { workspace = true }
log = { workspace = true }
hashbrown = { workspace = true }
num-traits = { workspace = true }
portable-atomic = { workspace = true }

[dev-dependencies]
burn-tensor = { path = "../burn-tensor", version = "0.19.0", default-features = false, features = [
  "export_tests",
] }

[package.metadata.docs.rs]
features = ["default"]
rustdoc-args = ["--cfg", "docsrs"]
