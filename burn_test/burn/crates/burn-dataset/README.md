# Burn Dataset

> [Burn](https://github.com/tracel-ai/burn) dataset library

[![Current Crates.io Version](https://img.shields.io/crates/v/burn-dataset.svg)](https://crates.io/crates/burn-dataset)
[![license](https://shields.io/badge/license-MIT%2FApache--2.0-blue)](https://github.com/tracel-ai/burn-dataset/blob/master/README.md)

The Burn Dataset library is designed to streamline your machine learning (ML) data pipeline creation
process. It offers a variety of dataset implementations, transformation functions, and data sources.

## Feature Flags

- `audio` - enables audio dataset (SpeechCommandsDataset). Run the following example to try it out:

  ```shell
  cargo run --example speech_commands --features audio
  ```
