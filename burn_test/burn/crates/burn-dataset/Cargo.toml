[package]
authors = ["nathanielsimard <<EMAIL>>"]
categories = ["science"]
description = "Library with simple dataset APIs for creating ML data pipelines"
edition.workspace = true
keywords = ["deep-learning", "machine-learning", "data"]
license.workspace = true
name = "burn-dataset"
readme.workspace = true
repository = "https://github.com/tracel-ai/burn/tree/main/crates/burn-dataset"
documentation = "https://docs.rs/burn-dataset"
version.workspace = true

[lints]
workspace = true

[features]
default = ["sqlite-bundled"]
doc = ["default"]
audio = ["hound"]
fake = ["dep:fake"]
sqlite = ["__sqlite-shared", "dep:rusqlite"]
sqlite-bundled = ["__sqlite-shared", "rusqlite/bundled"]
vision = ["dep:flate2", "dep:globwalk", "dep:burn-common", "dep:image"]
# internal
__sqlite-shared = [
    "dep:r2d2",
    "dep:r2d2_sqlite",
    "dep:serde_rusqlite",
    "dep:image",
    "dep:gix-tempfile",
]
dataframe = ["dep:polars"]

[dependencies]
burn-common = { path = "../burn-common", version = "0.19.0", optional = true, features = [
    "network",
] }
csv = { workspace = true }
derive-new = { workspace = true }
dirs = { workspace = true }
fake = { workspace = true, optional = true }
flate2 = { workspace = true, optional = true }
gix-tempfile = { workspace = true, optional = true }
globwalk = { workspace = true, optional = true }
hound = { workspace = true, optional = true }
image = { workspace = true, optional = true }
polars = { workspace = true, optional = true }
r2d2 = { workspace = true, optional = true }
r2d2_sqlite = { workspace = true, optional = true }
rand = { workspace = true, features = ["std"] }
rmp-serde = { workspace = true }
rusqlite = { workspace = true, optional = true }
sanitize-filename = { workspace = true }
serde = { workspace = true, features = ["std", "derive"] }
serde_json = { workspace = true, features = ["std"] }
serde_rusqlite = { workspace = true, optional = true }
strum = { workspace = true }
tempfile = { workspace = true }
thiserror = { workspace = true }

[dev-dependencies]
rayon = { workspace = true }
rstest = { workspace = true }
fake = { workspace = true }

[package.metadata.cargo-udeps.ignore]
normal = ["strum", "strum_macros"]

[package.metadata.docs.rs]
features = ["doc"]
rustdoc-args = ["--cfg", "docsrs"]
