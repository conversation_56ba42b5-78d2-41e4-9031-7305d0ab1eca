[package]
authors = ["nathanielsimard <<EMAIL>>"]
categories = ["science", "no-std", "embedded", "wasm"]
description = "Flexible and Comprehensive Deep Learning Framework in Rust"
documentation = "https://docs.rs/burn"
edition.workspace = true
keywords = ["deep-learning", "machine-learning", "tensor", "pytorch", "ndarray"]
license.workspace = true
name = "burn"
readme.workspace = true
repository = "https://github.com/tracel-ai/burn"
rust-version = "1.88"
version.workspace = true

[lints]
workspace = true

[features]
default = [
    "burn-core/default",
    "burn-train?/default",
    "burn-collective?/default",
    "std",
    # Backends
    "burn-candle?/default",
    "burn-ndarray?/default",
    "burn-tch?/default",
    "burn-wgpu?/default",
    "burn-router?/default",
    "burn-remote?/default",
    "burn-cuda?/default",
    "burn-autodiff?/default",
    "burn-rocm?/default",
]
doc = [
    "default",
    "train",
    "burn-core/doc",
    "burn-train/doc",
    "burn-collective/doc",
    # Backends
    "burn-candle/doc",
    "burn-ndarray/doc",
    "burn-tch/doc",
    "burn-wgpu/doc",
    "burn-router/doc",
    "burn-cuda/doc",
    "burn-autodiff?/std",
    "burn-rocm/doc",

]
std = [
    "burn-core/std",
    # Backends
    "burn-candle?/std",
    "burn-ndarray?/std",
    "burn-wgpu?/std",
    "burn-router?/std",
    "burn-cuda?/std",
    "burn-autodiff?/std",
    "burn-rocm?/std",
]

network = ["burn-core/network"]

# Training with full features
train = ["burn-train", "autodiff", "dataset"]

## Includes the Text UI (progress bars, metric plots)
tui = ["burn-train?/tui"]

##  Includes system info metrics (CPU/GPU usage, etc)
metrics = ["burn-train?/sys-metrics"]

# Datasets
dataset = ["burn-core/dataset"]

sqlite = ["burn-core/sqlite"]
sqlite-bundled = ["burn-core/sqlite-bundled"]

# Custom deserializer for Record that is helpful for importing data, such as PyTorch pt files.
record-item-custom-serde = ["burn-core/record-item-custom-serde"]
# Serialization formats
experimental-named-tensor = ["burn-core/experimental-named-tensor"]


audio = ["burn-core/audio"]
vision = ["burn-core/vision"]

# Backend
ir = ["burn-ir"]
autodiff = ["burn-autodiff"]
fusion = ["ir", "burn-wgpu?/fusion", "burn-cuda?/fusion", "burn-rocm?/fusion"]

## Backend features
accelerate = ["burn-candle?/accelerate", "burn-ndarray?/blas-accelerate"]
autotune = ["burn-wgpu?/autotune", "burn-cuda?/autotune", "burn-rocm?/autotune"]
autotune-checks = [
    "burn-wgpu?/autotune-checks",
    "burn-cuda?/autotune-checks",
    "burn-rocm?/autotune-checks",
]
blas-netlib = ["burn-ndarray?/blas-netlib"]
openblas = ["burn-ndarray?/blas-openblas"]
openblas-system = ["burn-ndarray?/blas-openblas-system"]
remote = ["burn-remote/client", "ir"]
router = ["burn-router", "ir"]
server = ["burn-remote/server"]
simd = ["burn-ndarray?/simd"]
template = ["burn-wgpu?/template"]
collective = ["burn-collective", "burn-train?/ddp"]

candle = ["burn-candle"]
candle-cuda = ["candle", "burn-candle/cuda"]
candle-metal = ["burn-candle?/metal"]
cuda = ["burn-cuda"]
rocm = ["burn-rocm"]
ndarray = ["burn-ndarray"]
tch = ["burn-tch"]
vulkan = ["wgpu", "burn-wgpu/vulkan"]
webgpu = ["wgpu", "burn-wgpu/webgpu"]
metal = ["wgpu", "burn-wgpu/metal"]
wgpu = ["burn-wgpu"]

compilation-cache = [
    "burn-cuda?/compilation-cache",
    "burn-rocm?/compilation-cache",
]

[dependencies]

# ** Please make sure all dependencies support no_std when std is disabled **

burn-core = { path = "../burn-core", version = "0.19.0", default-features = false }
burn-train = { path = "../burn-train", version = "0.19.0", optional = true, default-features = false }
burn-collective = { path = "../burn-collective", version = "0.19.0", optional = true, default-features = false }

# Backends
burn-autodiff = { path = "../burn-autodiff", version = "0.19.0", optional = true, default-features = false }
burn-candle = { path = "../burn-candle", version = "0.19.0", optional = true }
burn-cuda = { path = "../burn-cuda", version = "0.19.0", optional = true, default-features = false }
burn-rocm = { path = "../burn-rocm", version = "0.19.0", optional = true, default-features = false }
burn-ndarray = { path = "../burn-ndarray", version = "0.19.0", optional = true, default-features = false }
burn-remote = { path = "../burn-remote", version = "0.19.0", default-features = false, optional = true }
burn-router = { path = "../burn-router", version = "0.19.0", default-features = false, optional = true }
burn-tch = { path = "../burn-tch", version = "0.19.0", optional = true }
burn-wgpu = { path = "../burn-wgpu", version = "0.19.0", optional = true, default-features = false }
burn-ir = { path = "../burn-ir", version = "0.19.0", optional = true, default-features = false }
