[package]
authors = ["nathanielsimard <<EMAIL>>"]
categories = ["science"]
description = "Kernel fusion backend decorator for the Burn framework"
edition.workspace = true
keywords = ["deep-learning", "machine-learning", "data"]
license.workspace = true
name = "burn-fusion"
readme.workspace = true
repository = "https://github.com/tracel-ai/burn/tree/main/crates/burn-fusion"
documentation = "https://docs.rs/burn-fusion"
version.workspace = true

[lints]
workspace = true

[features]
default = ["std"]
std = ["serde/std"]
doc = ["default"]
memory-checks = ["std"]

[dependencies]
burn-tensor = { path = "../burn-tensor", version = "0.19.0" }
burn-common = { path = "../burn-common", version = "0.19.0" }
burn-ir = { path = "../burn-ir", version = "0.19.0" }

hashbrown = { workspace = true }
derive-new = { workspace = true }
spin = { workspace = true }
log = { workspace = true }
serde = { workspace = true }
half = { workspace = true }

[package.metadata.docs.rs]
features = ["doc"]
rustdoc-args = ["--cfg", "docsrs"]
