[package]
authors = ["<PERSON><PERSON><PERSON><PERSON> (@antimora)"]
categories = []
description = "Common crate for the Burn framework"
edition.workspace = true
keywords = []
license.workspace = true
name = "burn-common"
readme.workspace = true
repository = "https://github.com/tracel-ai/burn/tree/main/crates/burn-common"
documentation = "https://docs.rs/burn-common"
version.workspace = true

[lints]
workspace = true

[features]
default = ["std", "cubecl-common/default"]
std = ["cubecl-common/std"]
doc = ["default"]
network = ["dep:indicatif", "dep:reqwest", "dep:tokio"]
rayon = ["dep:rayon"]

[dependencies]
serde = { workspace = true }

# Network downloader
indicatif = { workspace = true, optional = true }
reqwest = { workspace = true, optional = true }
tokio = { workspace = true, optional = true }

# Parallel
rayon = { workspace = true, optional = true }
cubecl-common = { workspace = true, default-features = false, features = [
    "serde",
] }

[dev-dependencies]
dashmap = { workspace = true }

[package.metadata.docs.rs]
features = ["doc"]
rustdoc-args = ["--cfg", "docsrs"]
