[package]
authors = ["nathanielsimard <<EMAIL>>"]
categories = ["science"]
description = "Backend extension for collective calculations."
edition.workspace = true
keywords = ["deep-learning", "machine-learning", "collective"]
license.workspace = true
name = "burn-collective"
readme.workspace = true
repository = "https://github.com/tracel-ai/burn/tree/main/crates/burn-collective"
documentation = "https://docs.rs/burn-collective"
version.workspace = true

[lints]
workspace = true

[features]
default = []
doc = []
orchestrator = ["burn-communication/websocket"]

# Backends for testing
test-ndarray = ["burn-ndarray"]
test-wgpu = ["burn-wgpu", "burn-wgpu/webgpu"]
test-metal = ["burn-wgpu", "burn-wgpu/metal"]
test-vulkan = ["burn-wgpu", "burn-wgpu/vulkan"]
test-cuda = ["burn-cuda"]

[dependencies]
burn-tensor = { path = "../burn-tensor", version = "0.19.0", default-features = true }
burn-common = { path = "../burn-common", version = "0.19.0", default-features = true }

log = { workspace = true }

burn-communication = { path = "../burn-communication", version = "0.19.0", features = [
    "data-service",
    "websocket",
] }
tokio = { workspace = true, features = [
    "rt-multi-thread",
    "sync",
    "signal",
    "time",
] }
serde = { workspace = true, features = ["derive"] }
rmp-serde = { workspace = true }
bytes = { workspace = true }
futures = { workspace = true }
tokio-util = { workspace = true }

# Tests
burn-ndarray = { path = "../burn-ndarray", version = "0.19.0", optional = true }
burn-wgpu = { path = "../burn-wgpu", version = "0.19.0", optional = true }
burn-cuda = { path = "../burn-cuda", version = "0.19.0", optional = true }

[dev-dependencies]
serial_test = { workspace = true }


[package.metadata.docs.rs]
features = ["doc"]
rustdoc-args = ["--cfg", "docsrs"]
