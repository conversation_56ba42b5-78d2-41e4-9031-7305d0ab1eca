[package]
name = "burn-collective-multinode-tests"
version.workspace = true
edition.workspace = true
license.workspace = true

[features]
default = ["ndarray"]
ndarray = ["burn/ndarray"]

[dependencies]
burn = { path = "../../burn", default-features = false, features = ["std"] }
burn-common = { path = "../../burn-common", default-features = false }
burn-collective = { path = "..", features = ["orchestrator"] }
burn-communication = { path = "../../burn-communication" }

tokio = { workspace = true, features = ["rt-multi-thread", "process"] }

serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
interprocess = "2.2.3"
rmp-serde = { workspace = true }
tokio-util = { workspace = true, features = ["codec"] }
tokio-serde = { version = "0.9.0", features = ["messagepack"] }
futures = { workspace = true }


[[bin]]
name = "global"
path = "src/bin/global.rs"

[[bin]]
name = "node"
path = "src/bin/node.rs"
