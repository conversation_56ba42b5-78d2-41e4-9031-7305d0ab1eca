[package]
authors = ["louisfd <<EMAIL>>"]
categories = ["science"]
description = "Candle backend for the Burn framework"
edition.workspace = true
keywords = ["deep-learning", "machine-learning", "data"]
license.workspace = true
name = "burn-candle"
readme.workspace = true
repository = "https://github.com/tracel-ai/burn/tree/main/crates/burn-candle"
documentation = "https://docs.rs/burn-candle"
version.workspace = true

[lints]
workspace = true

[features]
default = ["std"]
std = []
doc = ["default"]
cuda = ["candle-core/cuda"]
metal = ["candle-core/metal"]
accelerate = ["candle-core/accelerate"]

[dependencies]
derive-new = { workspace = true }
burn-tensor = { path = "../burn-tensor", version = "0.19.0", default-features = false }
burn-common = { path = "../burn-common", version = "0.19.0", default-features = false }
half = { workspace = true }
candle-core = { workspace = true }

[dev-dependencies]
burn-autodiff = { path = "../burn-autodiff", version = "0.19.0", default-features = false, features = [
    "export_tests",
] }
burn-tch = { path = "../burn-tch", version = "0.19.0", default-features = false, features = [
] }
burn-tensor = { path = "../burn-tensor", version = "0.19.0", default-features = false, features = [
    "export_tests",
] }

[package.metadata.docs.rs]
features = ["doc"]
rustdoc-args = ["--cfg", "docsrs"]
