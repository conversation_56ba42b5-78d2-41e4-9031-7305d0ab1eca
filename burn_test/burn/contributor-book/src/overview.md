# Overview

Welcome to The Burn Contributor's Book 👋

This book will help you get acquainted with the internals of the Burn deep learning framework and
provide some detailed guidance on how to contribute to the project.

We have crafted some sections for you:

- [Getting Started](./getting-started): Much like the [Burn Book](https://burn.dev/books/burn/) which
  targets users, we'll start with the fundamentals, guiding you through tasks like setting up the
  development environment, running tests, and what you should check prior to each commit.

- [Project Architecture](./project-architecture): This section will give you an in-depth look at the
  architecture of Burn.

- [Guides](./guides): We provide some guides on how to do specific tasks, such as adding a new
  operations to Burn.

- [Frequently Encountered Issues](./frequently-encountered-issues): If you are running into an issue
  that has you stumped, this is the section to check out prior to asking on the
  [Discord](https://discord.gg/uPEBbYYDB6). It's a collection of errors encountered by contributors,
  what caused them, and how they were resolved.

As this book is geared towards contributors and not towards users of Burn, we'll assume you have a
good understanding of software development, but will make efforts to explain anything outside of
that scope, or at least provide links to resources that explain it better than we can.
