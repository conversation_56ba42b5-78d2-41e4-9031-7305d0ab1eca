name: <PERSON><PERSON><PERSON> PRs

on:
  schedule:
    - cron: '0 6 * * MON' # Monday at 6:00am UTC
  workflow_dispatch: # allows to manually trigger the workflow as well

# The minimum permissions required to run this Action
permissions:
  contents: write
  pull-requests: write
  checks: read

jobs:
  combine-prs:
    runs-on: ubuntu-latest

    steps:
      - name: combine-prs
        id: combine-prs
        uses: github/combine-prs@v5.2.0 # where X.X.X is the latest version
        with:
          labels: dependencies,automated
