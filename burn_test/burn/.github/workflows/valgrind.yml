name: valgrind

on:
  schedule:
    - cron: '0 23 * * WED' # Run every Wednesday at 23:00 (UTC)

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  valgrind:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Install llvmpipe and lavapipe
      uses: tracel-ai/github-actions/setup-llvmpipe-lavapipe@v3

    - name: Install valgrind
      run: |
        sudo apt-get install valgrind

    - name: Run cargo-valgrind
      env:
        CARGO_TARGET_X86_64_UNKNOWN_LINUX_GNU_RUNNER: "valgrind -s --leak-check=full --show-leak-kinds=all --error-exitcode=1"
      # Looking for vulnerabilities
      run: |
        cargo test
