name: CI

on:
  push:
    branches:
      - main
    paths:
      - 'Cargo.lock'
      - '**.rs'
      - '**.sh'
      - '**.ps1'
      - '**.yml'
      - '**.toml'
      - '!**.md'
      - '!LICENSE-APACHE'
      - '!LICENSE-MIT'
  pull_request:
    types: [opened, synchronize]
    paths:
      - 'Cargo.lock'
      - '**.rs'
      - '**.sh'
      - '**.ps1'
      - '**.yml'
      - '**.toml'
      - '!**.md'
      - '!LICENSE-APACHE'
      - '!LICENSE-MIT'

env:
  # Note: It is not possible to define top level env vars and pass them to composite actions.
  # To work around this issue we use inputs and define all the env vars here.

  RUST_PREVIOUS_VERSION: 1.88.0

  # Dependency versioning
  # from wgpu repo: https://github.com/gfx-rs/wgpu/blob/trunk/.github/workflows/ci.yml

  # Sourced from https://vulkan.lunarg.com/sdk/home#linux
  VULKAN_SDK_VERSION: "1.3.268"

  # Sourced from https://archive.mesa3d.org/. Bumping this requires
  # updating the mesa build in https://github.com/gfx-rs/ci-build and creating a new release.
  MESA_VERSION: "24.2.3"
  # Corresponds to https://github.com/gfx-rs/ci-build/releases
  MESA_CI_BINARY_BUILD: "build19"

  # Sourced from https://www.nuget.org/packages/Microsoft.Direct3D.WARP
  WARP_VERSION: "1.0.13"
  # Sourced from https://github.com/microsoft/DirectXShaderCompiler/releases
  # Must also be changed in shaders.yaml
  DXC_RELEASE: "v1.8.2407"
  DXC_FILENAME: "dxc_2024_07_31_clang_cl.zip"

  # Mozilla Grcov
  GRCOV_LINK: "https://github.com/mozilla/grcov/releases/download"
  GRCOV_VERSION: "0.8.19"

  # Test in release mode (make it an empty string to test in debug mode)
  TEST_RELEASE_FLAG: "--release"

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  prepare-checks:
    runs-on: ubuntu-latest
    outputs:
      rust-prev-version: ${{ env.RUST_PREVIOUS_VERSION }}
      gcp_runners_image_family: ${{ env.GCP_RUNNERS_IMAGE_FAMILY }}
      gcp_runners_machine_type: ${{ env.GCP_RUNNERS_MACHINE_TYPE }}
      gcp_runners_zone: ${{ env.GCP_RUNNERS_ZONE }}
    steps:
      - name: Do Nothing
        if: false
        run: echo

  code-quality:
    runs-on: ubuntu-22.04
    needs: prepare-checks
    strategy:
      matrix:
        rust: [stable]
        include:
          - rust: stable
            toolchain: stable
    steps:
      - name: Setup Rust
        uses: tracel-ai/github-actions/setup-rust@v3
        with:
          rust-toolchain: ${{ matrix.toolchain }}
          cache-key: ${{ matrix.rust }}-linux
      # --------------------------------------------------------------------------------
      - name: Audit
        run: cargo xtask check audit
      # --------------------------------------------------------------------------------
      - name: Format
        shell: bash
        env:
          # work around for colors
          # see: https://github.com/rust-lang/rustfmt/issues/3385
          TERM: xterm-256color
        run: cargo xtask check format
      # --------------------------------------------------------------------------------
      - name: Lint
        run: cargo xtask check lint
      # --------------------------------------------------------------------------------
      - name: Typos
        uses: tracel-ai/github-actions/check-typos@v3

  documentation:
    runs-on: ubuntu-22.04
    needs: prepare-checks
    strategy:
      matrix:
        rust: [stable]
        include:
          - rust: stable
            toolchain: stable
    steps:
      - name: Setup Rust
        uses: tracel-ai/github-actions/setup-rust@v3
        with:
          rust-toolchain: ${{ matrix.toolchain }}
          cache-key: ${{ matrix.rust }}-linux
      # --------------------------------------------------------------------------------
      - name: Documentation Build
        run: cargo xtask doc build
      # --------------------------------------------------------------------------------
      - name: Documentation Tests
        run: cargo xtask doc tests

  linux-std-tests:
    runs-on: ubuntu-22.04
    needs: [prepare-checks, code-quality]
    env:
      DISABLE_WGPU_SPIRV: '1'
      # disable incremental compilation (reduces artifact size)
      CARGO_PROFILE_TEST_INCREMENTAL: 'false'
    strategy:
      matrix:
        rust: [stable, prev]
        include:
          - rust: stable
            toolchain: stable
            coverage: --enable-coverage
          - rust: prev
            toolchain: ${{ needs.prepare-checks.outputs.rust-prev-version }}
    steps:
      - name: Setup Rust
        uses: tracel-ai/github-actions/setup-rust@v3
        with:
          rust-toolchain: ${{ matrix.toolchain }}
          cache-key: ${{ matrix.rust }}-linux
          # Disable cache on linux-std (stable) runner which currently always runs out of disk space with tests + coverage
          enable-cache: ${{ matrix.rust != 'stable' }}
      # # --------------------------------------------------------------------------------
      - name: Install grcov
        if: matrix.rust == 'stable'
        shell: bash
        run: |
          curl -L "$GRCOV_LINK/v$GRCOV_VERSION/grcov-x86_64-unknown-linux-musl.tar.bz2" |
          tar xj -C $HOME/.cargo/bin
          cargo xtask coverage install
      # --------------------------------------------------------------------------------
      - name: Tests
        run: cargo xtask ${{ matrix.coverage }} test ${{ env.TEST_RELEASE_FLAG }} --ci github-runner
      # --------------------------------------------------------------------------------
      - name: Generate lcov.info
        if: matrix.rust == 'stable'
        # /* is to exclude std library code coverage from analysis
        run: cargo xtask coverage generate --ignore "/*,xtask/*,examples/*" --profile release
      # --------------------------------------------------------------------------------
      - name: Codecov upload lcov.info
        if: matrix.rust == 'stable'
        uses: codecov/codecov-action@v5
        with:
          files: lcov.info
          token: ${{ secrets.CODECOV_TOKEN }}

  linux-no-std-tests:
    runs-on: ubuntu-22.04
    needs: [prepare-checks, code-quality]
    strategy:
      matrix:
        rust: [stable, prev]
        include:
          - rust: stable
            toolchain: stable
          - rust: prev
            toolchain: ${{ needs.prepare-checks.outputs.rust-prev-version }}
    steps:
      - name: Setup Rust
        uses: tracel-ai/github-actions/setup-rust@v3
        with:
          rust-toolchain: ${{ matrix.toolchain }}
          cache-key: ${{ matrix.rust }}-linux-no-std
      # --------------------------------------------------------------------------------
      - name: Setup Linux runner
        uses: tracel-ai/github-actions/setup-linux@v3
        with:
          vulkan-sdk-version: ${{ env.VULKAN_SDK_VERSION }}
          mesa-version: ${{ env.MESA_VERSION }}
          mesa-ci-build-version: ${{ env.MESA_CI_BINARY_BUILD }}
      # --------------------------------------------------------------------------------
      - name: Crates Build
        run: cargo xtask --context no-std build --ci
      # --------------------------------------------------------------------------------
      - name: Crates Tests
        run: cargo xtask --context no-std test ${{ env.TEST_RELEASE_FLAG }} --ci github-runner

  windows-std-tests:
    runs-on: windows-2022
    needs: [prepare-checks, code-quality]
    env:
      DISABLE_WGPU: '1'
    # Keep the stragegy to be able to easily add new rust versions if required
    strategy:
      matrix:
        rust: [stable]
        include:
          - rust: stable
            toolchain: stable
    steps:
      - name: Setup Rust
        uses: tracel-ai/github-actions/setup-rust@v3
        with:
          rust-toolchain: ${{ matrix.toolchain }}
          cache-key: ${{ matrix.rust }}-windows
      # --------------------------------------------------------------------------------
      - name: Setup Windows runner
        if: env.DISABLE_WGPU != '1'
        uses: tracel-ai/github-actions/setup-windows@v3
        with:
          dxc-release: ${{ env.DXC_RELEASE }}
          dxc-filename: ${{ env.DXC_FILENAME }}
          mesa-version: ${{ env.MESA_VERSION }}
          warp-version: ${{ env.WARP_VERSION }}
      # --------------------------------------------------------------------------------
      - name: Tests
        run: cargo xtask test ${{ env.TEST_RELEASE_FLAG }} --ci github-runner
