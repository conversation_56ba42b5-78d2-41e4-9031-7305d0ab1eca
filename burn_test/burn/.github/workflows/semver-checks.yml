name: semver-checks

on:
  schedule:
    - cron: '0 21 * * THU' # Run every Thursday at 21:00 (UTC)
  push:
    tags:
      - 'v*.*.*' # Run when a new version is being published

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  semver-checks:
    runs-on: ubuntu-latest

    steps:
      - name: checkout
        uses: actions/checkout@v4

      - name: Check semantic versioning violations
        uses: obi1kenobi/cargo-semver-checks-action@v2
        with:
          # cargo-semver-checks uses `all-features` by default, but `burn`
          # publishes on crates.io with `default-features`
          feature-group: default-features
          # Exclude crates which are not published on crates.io
          exclude: burn-no-std-tests,onnx-tests,pytorch-tests
