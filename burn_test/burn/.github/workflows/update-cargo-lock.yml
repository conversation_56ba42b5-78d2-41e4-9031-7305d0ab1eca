name: Update Cargo.lock Pull Request
on:
  schedule:
    - cron: '0 6 * * TUE,WED,THU,FRI,SAT,SUN' # every day except Monday (reserved for dependabot)
  workflow_dispatch: # allows to manually trigger the workflow as well

jobs:
  update-cargo-lock-file:
    name: Update Cargo.lock file
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      # unmaintained since 2023
      - uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
      # we use a GitHub app token so that other workflows can react to the
      # creation of the Pull Request.
      # Following these guidelines -> https://github.com/peter-evans/create-pull-request/blob/main/docs/concepts-guidelines.md#authenticating-with-github-app-generated-tokens
      # For additional info -> https://github.com/actions/create-github-app-token
      - name: generate GitHub app token
        id: generate-app-token
        uses: actions/create-github-app-token@v2
        with:
          app-id: ${{ secrets.UPDATE_CARGO_LOCK_APP_ID }}
          private-key: ${{ secrets.UPDATE_CARGO_LOCK_APP_PRIVATE_KEY }}
      - name: cargo update
        run: cargo update
      - name: create pull request
        uses: peter-evans/create-pull-request@v7
        with:
          token: ${{ steps.generate-app-token.outputs.token }}
          branch: "create-pull-request/update-cargo-lock"
          title: "Cargo.lock update"
          body: |
            This PR has been automatically created by the [update-cargo-lock.yml](https://github.com/tracel-ai/burn/blob/main/.github/workflows/update-cargo-lock.yml) workflow.
          commit-message: |
            Update Cargo.lock

            This commit has been automatically generated by
            update-cargo-lock.yml workflow.
          labels: dependencies,automated
          delete-branch: true
