- [Overview](./overview.md)
- [Why Burn?](./motivation.md)
- [Getting started](./getting-started.md)
  - [Examples](./examples.md)
- [Basic Workflow: From Training to Inference](./basic-workflow/README.md)
  - [Model](./basic-workflow/model.md)
  - [Data](./basic-workflow/data.md)
  - [Training](./basic-workflow/training.md)
  - [Backend](./basic-workflow/backend.md)
  - [Inference](./basic-workflow/inference.md)
  - [Conclusion](./basic-workflow/conclusion.md)
- [Building Blocks](./building-blocks/README.md)
  - [Backend](./building-blocks/backend.md)
  - [Tensor](./building-blocks/tensor.md)
  - [Autodiff](./building-blocks/autodiff.md)
  - [Module](./building-blocks/module.md)
  - [Learner](./building-blocks/learner.md)
  - [Metric](./building-blocks/metric.md)
  - [Config](./building-blocks/config.md)
  - [Record](./building-blocks/record.md)
  - [Dataset](./building-blocks/dataset.md)
- [Custom Training Loop](./custom-training-loop.md)
- [Saving & Loading Models](./saving-and-loading.md)
- [Import Models](./import/README.md)
  - [ONNX Model](./import/onnx-model.md)
  - [PyTorch Model](./import/pytorch-model.md)
  - [Safetensors Model](./import/safetensors-model.md)
- [Models & Pre-Trained Weights](./models-and-pretrained-weights.md)
- [Quantization (Beta)](./quantization.md)
- [Advanced](./advanced/README.md)
  - [Backend Extension](./advanced/backend-extension/README.md)
    - [Custom `cubecl` Kernel](./advanced/backend-extension/custom-cubecl-kernel.md)
    - [Custom WGPU Kernel](./advanced/backend-extension/custom-wgpu-kernel.md)
  - [Custom Optimizer]()
  - [WebAssembly](./advanced/web-assembly.md)
  - [No-Std](./advanced/no-std.md)
