<svg xmlns="http://www.w3.org/2000/svg" id="export" class="canvas" preserveAspectRatio="xMidYMid meet" style="" width="112" height="124"><rect id="background" fill="#fff" pointer-events="all" width="112" height="124"/><g id="origin" transform="translate(5.077343750000001, 5.077343750000001) scale(1)"><g id="clusters" class="clusters"/><g id="edge-paths" class="edge-paths"><defs><marker id="arrowhead" viewBox="0 0 10 10" refX="9" refY="5" markerUnits="strokeWidth" markerWidth="8" markerHeight="6" orient="auto" style="fill: rgb(0, 0, 0);"><path d="M 0 0 L 10 5 L 0 10 L 4 5 z" style="stroke-width: 1;"/></marker><marker id="arrowhead-select" viewBox="0 0 10 10" refX="9" refY="5" markerUnits="strokeWidth" markerWidth="8" markerHeight="6" orient="auto" style="fill: rgb(238, 0, 0);"><path d="M 0 0 L 10 5 L 0 10 L 4 5 z" style="stroke-width: 1;"/></marker><marker id="arrowhead-hover" viewBox="0 0 10 10" refX="9" refY="5" markerUnits="strokeWidth" markerWidth="8" markerHeight="6" orient="auto"><path d="M 0 0 L 10 5 L 0 10 L 4 5 z" style="stroke-width: 1;"/></marker></defs></g><g id="edge-labels" class="edge-labels"/><g id="nodes" class="nodes"><g id="node-name-conv.conv1" class="node graph-node" transform="translate(0,60)" style=""><g class="node-item node-item-type" transform="translate(0,0)"><path d="M5,0h91.546875a5,5 0 0 1 5,5v16a0,0 0 0 1 0,0h-101.546875a0,0 0 0 1 0,0v-16a5,5 0 0 1 5,-5z" style="stroke: rgb(0, 0, 0); fill: rgb(0, 0, 0); stroke-width: 0;"/><text x="6" y="15" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 11px; text-rendering: geometricprecision; user-select: none; fill: rgb(255, 255, 255);">conv.conv1</text><title>?</title></g><g class="node-attribute-list" transform="translate(0,21)"><path d="M0,0h101.546875a0,0 0 0 1 0,0v27a5,5 0 0 1 -5,5h-91.546875a5,5 0 0 1 -5,-5v-27a0,0 0 0 1 0,0z" style="stroke: rgb(0, 0, 0); fill: rgb(255, 255, 255); stroke-width: 0;"/><g class="node-attribute"><text xml:space="preserve" x="6" y="13" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 9px; font-weight: normal; text-rendering: geometricprecision; user-select: none;"><title>float32[2,2,2,2]</title><tspan style="font-weight: bold;">weight</tspan><tspan>〈2×2×2×2〉</tspan></text></g><g class="node-attribute"><text xml:space="preserve" x="6" y="26" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 9px; font-weight: normal; text-rendering: geometricprecision; user-select: none;"><title>float32[2]</title><tspan style="font-weight: bold;">bias</tspan><tspan>〈2〉</tspan></text></g><line class="node" x1="0" x2="101.546875" y1="0" y2="0" style="stroke: rgb(51, 51, 51); fill: none; stroke-width: 1px;"/></g><path class="node node-border" d="M5,0h91.546875a5,5 0 0 1 5,5v43a5,5 0 0 1 -5,5h-91.546875a5,5 0 0 1 -5,-5v-43a5,5 0 0 1 5,-5z" style="stroke: rgb(51, 51, 51); fill: none; stroke-width: 1px;"/></g><g id="node-name-conv.conv2" class="node graph-node" transform="translate(0,0)" style=""><g class="node-item node-item-type" transform="translate(0,0)"><path d="M5,0h91.546875a5,5 0 0 1 5,5v16a0,0 0 0 1 0,0h-101.546875a0,0 0 0 1 0,0v-16a5,5 0 0 1 5,-5z" style="stroke: rgb(0, 0, 0); fill: rgb(0, 0, 0); stroke-width: 0;"/><text x="6" y="15" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 11px; text-rendering: geometricprecision; user-select: none; fill: rgb(255, 255, 255);">conv.conv2</text><title>?</title></g><g class="node-attribute-list" transform="translate(0,21)"><path d="M0,0h101.546875a0,0 0 0 1 0,0v14a5,5 0 0 1 -5,5h-91.546875a5,5 0 0 1 -5,-5v-14a0,0 0 0 1 0,0z" style="stroke: rgb(0, 0, 0); fill: rgb(255, 255, 255); stroke-width: 0;"/><g class="node-attribute"><text xml:space="preserve" x="6" y="13" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe WPC&quot;, &quot;Segoe UI&quot;, Ubuntu, &quot;Droid Sans&quot;, sans-serif, &quot;PingFang SC&quot;; font-size: 9px; font-weight: normal; text-rendering: geometricprecision; user-select: none;"><title>float32[2,2,2,2]</title><tspan style="font-weight: bold;">weight</tspan><tspan>〈2×2×2×2〉</tspan></text></g><line class="node" x1="0" x2="101.546875" y1="0" y2="0" style="stroke: rgb(51, 51, 51); fill: none; stroke-width: 1px;"/></g><path class="node node-border" d="M5,0h91.546875a5,5 0 0 1 5,5v30a5,5 0 0 1 -5,5h-91.546875a5,5 0 0 1 -5,-5v-30a5,5 0 0 1 5,-5z" style="stroke: rgb(51, 51, 51); fill: none; stroke-width: 1px;"/></g></g></g></svg>