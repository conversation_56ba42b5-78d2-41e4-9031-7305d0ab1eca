# Models and Pre-Trained Weights

The [`models`](https://github.com/tracel-ai/models) repository contains definitions of different
deep learning models with examples for different domains like computer vision and natural language
processing.

This includes image classification models such as
[`MobileNetV2`](https://github.com/tracel-ai/models/tree/main/mobilenetv2-burn),
[`SqueezeNet`](https://github.com/tracel-ai/models/tree/main/squeezenet-burn) and
[`ResNet`](https://github.com/tracel-ai/models/tree/main/resnet-burn), object detection models such
as [`YOLOX`](https://github.com/tracel-ai/models/tree/main/yolox-burn) and language models like
[`BERT` and `RoBERTa`](https://github.com/tracel-ai/models/tree/main/bert-burn).

Be sure to check out the up-to-date
[collection of models](https://github.com/tracel-ai/models?tab=readme-ov-file#collection-of-official-models)
to get you started. Pre-trained weights are available for every supported architecture in this
collection. You will also find a spotlight of
[community contributed models](https://github.com/tracel-ai/models?tab=readme-ov-file#community-contributions).
