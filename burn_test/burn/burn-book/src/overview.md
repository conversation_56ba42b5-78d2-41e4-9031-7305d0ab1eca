# Overview

Welcome to The Burn Book 👋

This book will help you get started with the Burn deep learning framework, whether you are an
advanced user or a beginner. We have crafted some sections for you:

- [Basic Workflow: From Training to Inference](./basic-workflow): We'll start with the fundamentals,
  guiding you through the entire workflow, from training your models to deploying them for
  inference. This section lays the groundwork for your Burn expertise.

- [Building Blocks](./building-blocks): Dive deeper into Burn's core components, understanding how
  they fit together. This knowledge forms the basis for more advanced usage and customization.

- [Saving & Loading Models](./saving-and-loading.md): Learn how to easily save and load your trained
  models.

- [Custom Training Loop](./custom-training-loop.md): Gain the power to customize your training
  loops, fine-tuning your models to meet your specific requirements. This section empowers you to
  harness Burn's flexibility to its fullest.

- [Importing Models](./import): Learn how to import ONNX and PyTorch models, expanding your
  compatibility with other deep learning ecosystems.

- [Advanced](./advanced): Finally, venture into advanced topics, exploring <PERSON>'s capabilities at
  their peak. This section caters to those who want to push the boundaries of what's possible with
  <PERSON>.

Throughout the book, we assume a basic understanding of deep learning concepts, but we may refer to
additional material when it seems appropriate.
