use burn::{
    data::{
        dataloader::batcher::<PERSON><PERSON>,
        dataset::vision::MnistItem,
        dataset::{Dataset, InMemDataset},
    },
    prelude::*,
};
use ndarray::{Array2, Array3};
use serde::{Deserialize, Serialize};

const NUM_SYMBOLS: usize = 8;
const NUM_FACTORS: usize = 251;

#[derive(<PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct MnistBatcher {}

#[derive(<PERSON><PERSON>, Debug)]
pub struct MnistBatch<B: Backend> {
    pub images: Tensor<B, 3>,
    pub targets: Tensor<B, 1, Int>,
}

impl<B: Backend> Batcher<B, MnistItem, MnistBatch<B>> for MnistBatcher {
    fn batch(&self, items: Vec<MnistItem>, device: &B::Device) -> MnistBatch<B> {
        let images = items
            .iter()
            .map(|item| TensorData::from(item.image).convert::<B::FloatElem>())
            .map(|data| Tensor::<B, 2>::from_data(data, device))
            .map(|tensor| tensor.reshape([1, 28, 28]))
            // Normalize: scale between [0,1] and make the mean=0 and std=1
            // values mean=0.1307,std=0.3081 are from the PyTorch MNIST example
            // https://github.com/pytorch/examples/blob/54f4572509891883a947411fd7239237dd2a39c3/mnist/main.py#L122
            .map(|tensor| ((tensor / 255) - 0.1307) / 0.3081)
            .collect();

        let targets = items
            .iter()
            .map(|item| {
                Tensor::<B, 1, Int>::from_data([(item.label as i64).elem::<B::IntElem>()], device)
            })
            .collect();

        let images = Tensor::cat(images, 0);
        let targets = Tensor::cat(targets, 0);

        MnistBatch { images, targets }
    }
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct FactorDataItem {
    pub features: Vec<Vec<f32>>,  
    pub label: f32,
}

pub struct FactorDataset {
    dataset: InMemDataset<FactorDataItem>,
    raw_data: Array3<f32>,
}

impl Dataset<FactorDataItem> for FactorDataset {
    fn get(&self, index: usize) -> Option<FactorDataItem> {
        self.dataset.get(index)
    }

    fn len(&self) -> usize {
        self.dataset.len()
    }
}

impl FactorDataset {
    pub fn train() {

    }

    pub fn test() {
        
    }
}